import React, { useState, useEffect, useRef } from 'react';
import {
    <PERSON>,
    Typography,
    Alert,
    CircularProgress,
    Button,
    IconButton,
    Divider,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Collapse,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Paper
} from '@mui/material';
import {
    Save as SaveIcon,
    Undo as RevertIcon,
    Folder as FolderIcon,
    FolderOpen as FolderOpenIcon,
    InsertDriveFile as FileIcon,
    Add as AddIcon,
    Delete as DeleteIcon,
    ExpandLess,
    ExpandMore,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import Editor from '@monaco-editor/react';
import { generateFileChanges, calculatePayloadReduction, shouldUseDiff } from '../utils/diffUtils';

interface MonacoWorkspaceProps {
    interviewUuid?: string;
    onWorkspaceReady?: (workspaceId: string) => void;
    onError?: (error: string) => void;
    height?: string | number;
    minHeight?: string | number;
}

interface FileNode {
    name: string;
    path: string;
    type: 'file' | 'folder';
    content?: string;
    language?: string;
    children?: FileNode[];
    isExpanded?: boolean;
    lastModified?: Date;
}

interface WorkspaceState {
    files: { [path: string]: string };
    savedFiles: { [path: string]: string };
    fileTree: FileNode[];
    selectedFile: string | null;
    hasUnsavedChanges: boolean;
}

const MonacoWorkspace: React.FC<MonacoWorkspaceProps> = ({
    interviewUuid,
    onWorkspaceReady,
    onError,
    height = '100%',
    minHeight = '500px'
}) => {
    const [workspace, setWorkspace] = useState<WorkspaceState>({
        files: {},
        savedFiles: {},
        fileTree: [],
        selectedFile: null,
        hasUnsavedChanges: false
    });

    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [newFileDialog, setNewFileDialog] = useState(false);
    const [newFileName, setNewFileName] = useState('');
    const [newFileType, setNewFileType] = useState<'file' | 'folder'>('file');

    const editorRef = useRef<any>(null);
    const workspaceNameRef = useRef<string>(`workspace_${interviewUuid || Date.now()}`);

    // Initialize workspace when component mounts
    useEffect(() => {
        if (interviewUuid && !isInitialized) {
            initializeWorkspace();
        }
    }, [interviewUuid, isInitialized]);

    // Listen for file updates from chat responses
    useEffect(() => {
        const handleChatFileUpdate = (event: CustomEvent) => {
            const { files } = event.detail;
            if (files && typeof files === 'object') {
                updateFilesFromChat(files);
            }
        };

        window.addEventListener('monaco-workspace-update', handleChatFileUpdate as EventListener);

        return () => {
            window.removeEventListener('monaco-workspace-update', handleChatFileUpdate as EventListener);
        };
    }, []);

    const initializeWorkspace = async () => {
        if (!interviewUuid) {
            setError('Interview UUID is required for workspace initialization');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            console.log('🚀 Initializing Monaco workspace for interview:', interviewUuid);

            // Check if we have mock data to populate the workspace
            const bypassApiKey = import.meta.env.VITE_BYPASS_API_KEY === 'true' ||
                localStorage.getItem('BYPASS_API_KEY') === 'true';

            if (bypassApiKey) {
                console.log('🧪 Using mock data for workspace initialization');
                await initializeFromMockData();
            } else {
                console.log('📡 Fetching workspace structure from backend');
                await fetchWorkspaceStructure();
            }

            setIsInitialized(true);
            onWorkspaceReady?.(workspaceNameRef.current);

        } catch (error: any) {
            console.error('❌ Error initializing workspace:', error);
            setError(error.message || 'Failed to initialize workspace');
            onError?.(error.message || 'Failed to initialize workspace');
        } finally {
            setIsLoading(false);
        }
    };

    const initializeFromMockData = async () => {
        let filesLoaded = false;

        try {
            // First try to load from filesystem
            const token = localStorage.getItem('token');
            const response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/structure`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data.structure) {
                    const files = flattenStructureToFiles(data.data.structure);
                    if (Object.keys(files).length > 0) {
                        const fileTree = createFileTreeFromStructure(data.data.structure);
                        console.log('📁 Loaded files from filesystem:', Object.keys(files));

                        setWorkspace(prev => ({
                            ...prev,
                            files,
                            savedFiles: { ...files },
                            fileTree,
                            selectedFile: Object.keys(files)[0] || null
                        }));
                        filesLoaded = true;
                    }
                }
            }

            // If no files found in filesystem, request backend to extract from mock data
            if (!filesLoaded) {
                console.log('📁 No files found in filesystem, requesting backend to extract from mock data');

                // Call backend API to extract files from mock data and create workspace
                const extractResponse = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/extract-mock`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (extractResponse.ok) {
                    const extractData = await extractResponse.json();
                    if (extractData.success && extractData.data.files) {
                        const files = extractData.data.files;
                        const fileTree = createFileTreeFromFiles(files);

                        console.log('📁 Extracted files from backend:', Object.keys(files));

                        setWorkspace(prev => ({
                            ...prev,
                            files,
                            savedFiles: { ...files },
                            fileTree,
                            selectedFile: Object.keys(files)[0] || null
                        }));
                        filesLoaded = true;
                    }
                } else {
                    console.error('❌ Failed to extract files from backend:', extractResponse.status);
                }
            }
        } catch (error) {
            console.error('❌ Error loading mock data:', error);
            // Fallback to empty workspace
            setWorkspace(prev => ({
                ...prev,
                fileTree: [],
                files: {},
                savedFiles: {}
            }));
        }
    };

    const fetchWorkspaceStructure = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/structure`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                // If workspace doesn't exist, create it first
                if (response.status === 404) {
                    console.log('📁 Workspace not found, creating new workspace');
                    await createWorkspaceOnBackend();
                    return;
                }
                throw new Error('Failed to fetch workspace structure');
            }

            const data = await response.json();

            if (data.success && data.data.structure) {
                const files = flattenStructureToFiles(data.data.structure);
                const fileTree = createFileTreeFromStructure(data.data.structure);

                console.log('📁 Loaded files from filesystem:', Object.keys(files));

                setWorkspace(prev => ({
                    ...prev,
                    files,
                    savedFiles: { ...files },
                    fileTree,
                    selectedFile: Object.keys(files)[0] || null
                }));
            } else {
                console.log('📁 No structure found in response, initializing empty workspace');
                setWorkspace(prev => ({
                    ...prev,
                    fileTree: [],
                    files: {},
                    savedFiles: {}
                }));
            }
        } catch (error) {
            console.error('❌ Error fetching workspace structure:', error);
            // Initialize empty workspace if fetch fails
            setWorkspace(prev => ({
                ...prev,
                fileTree: [],
                files: {},
                savedFiles: {}
            }));
        }
    };

    const createWorkspaceOnBackend = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`http://localhost:3001/api/workspace`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    workspaceName: workspaceNameRef.current
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create workspace');
            }

            console.log('✅ Workspace created successfully');

            // Initialize with empty workspace
            setWorkspace(prev => ({
                ...prev,
                fileTree: [],
                files: {},
                savedFiles: {}
            }));

        } catch (error) {
            console.error('❌ Error creating workspace:', error);
            setError('Failed to create workspace');
        }
    };

    // File extraction is now handled by backend CodeExtractor - removed frontend implementation

    const createFileTreeFromFiles = (files: { [path: string]: string }): FileNode[] => {
        const tree: FileNode[] = [];
        const pathMap: { [path: string]: FileNode } = {};

        // Sort paths to ensure directories are created before files
        const sortedPaths = Object.keys(files).sort();

        for (const filePath of sortedPaths) {
            const parts = filePath.split('/');
            let currentPath = '';

            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                const parentPath = currentPath;
                currentPath = currentPath ? `${currentPath}/${part}` : part;

                if (!pathMap[currentPath]) {
                    const isFile = i === parts.length - 1;
                    const node: FileNode = {
                        name: part,
                        path: currentPath,
                        type: isFile ? 'file' : 'folder',
                        content: isFile ? files[filePath] : undefined,
                        language: isFile ? getLanguageFromFilename(part) : undefined,
                        children: isFile ? undefined : [],
                        isExpanded: true
                    };

                    pathMap[currentPath] = node;

                    if (parentPath && pathMap[parentPath]) {
                        pathMap[parentPath].children!.push(node);
                    } else {
                        tree.push(node);
                    }
                }
            }
        }

        return tree;
    };

    const createFileTreeFromStructure = (structure: any, basePath: string = ''): FileNode[] => {
        const tree: FileNode[] = [];

        for (const [name, item] of Object.entries(structure)) {
            const currentPath = basePath ? `${basePath}/${name}` : name;

            if ((item as any).type === 'folder') {
                const folderNode: FileNode = {
                    name,
                    path: currentPath,
                    type: 'folder',
                    children: createFileTreeFromStructure((item as any).children, currentPath),
                    isExpanded: true
                };
                tree.push(folderNode);
            } else {
                const fileNode: FileNode = {
                    name,
                    path: currentPath,
                    type: 'file',
                    content: (item as any).content,
                    language: (item as any).language,
                    lastModified: new Date((item as any).lastModified)
                };
                tree.push(fileNode);
            }
        }

        return tree;
    };

    const flattenStructureToFiles = (structure: any, basePath: string = ''): { [path: string]: string } => {
        const files: { [path: string]: string } = {};

        for (const [name, item] of Object.entries(structure)) {
            const currentPath = basePath ? `${basePath}/${name}` : name;

            if ((item as any).type === 'folder') {
                Object.assign(files, flattenStructureToFiles((item as any).children, currentPath));
            } else {
                files[currentPath] = (item as any).content || '';
            }
        }

        return files;
    };

    const getLanguageFromFilename = (filename: string): string => {
        const ext = filename.split('.').pop()?.toLowerCase();
        const langMap: { [key: string]: string } = {
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'json': 'json',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'yml': 'yaml',
            'yaml': 'yaml',
            'xml': 'xml',
            'md': 'markdown',
            'txt': 'text',
            'env': 'bash',
            'sh': 'bash'
        };
        return langMap[ext || ''] || 'text';
    };

    const handleFileSelect = (filePath: string) => {
        setWorkspace(prev => ({
            ...prev,
            selectedFile: filePath
        }));
    };

    const handleEditorChange = (value: string | undefined) => {
        if (!workspace.selectedFile || value === undefined) return;

        setWorkspace(prev => {
            const newFiles = { ...prev.files, [prev.selectedFile!]: value };
            const hasChanges = Object.keys(newFiles).some(
                path => newFiles[path] !== prev.savedFiles[path]
            );

            return {
                ...prev,
                files: newFiles,
                hasUnsavedChanges: hasChanges
            };
        });
    };

    const handleSave = async () => {
        if (!workspace.hasUnsavedChanges) return;

        setIsLoading(true);
        try {
            const token = localStorage.getItem('token');

            // Generate file changes using diff algorithm
            const changes = generateFileChanges(workspace.savedFiles, workspace.files);

            // Calculate payload reduction
            const payloadStats = calculatePayloadReduction(workspace.files, changes);
            console.log('📊 Payload optimization:', {
                originalSize: `${(payloadStats.originalSize / 1024).toFixed(2)} KB`,
                optimizedSize: `${(payloadStats.optimizedSize / 1024).toFixed(2)} KB`,
                reduction: `${payloadStats.reduction.toFixed(1)}%`
            });

            // Use optimized endpoint if we have changes, otherwise fallback to full save
            let response;
            if (changes.length > 0 && payloadStats.reduction > 10) {
                // Use optimized diff-based endpoint
                console.log('🚀 Using optimized diff-based save');
                response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/apply-changes`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ changes })
                });
            } else {
                // Fallback to full file save for small changes or when diff isn't beneficial
                console.log('📄 Using full file save (diff not beneficial)');
                response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        files: workspace.files
                    })
                });
            }

            if (!response.ok) {
                throw new Error('Failed to save files to filesystem');
            }

            const result = await response.json();

            setWorkspace(prev => ({
                ...prev,
                savedFiles: { ...prev.files },
                hasUnsavedChanges: false
            }));

            console.log('✅ Files saved to filesystem successfully:', result.data);
        } catch (error) {
            console.error('❌ Error saving files:', error);
            setError('Failed to save files to filesystem');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRevert = async () => {
        setIsLoading(true);
        try {
            // Revert from local filesystem
            const token = localStorage.getItem('token');
            const response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/revert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Failed to revert from filesystem');
            }

            const result = await response.json();

            // Update workspace with filesystem state
            const fileTree = createFileTreeFromFiles(result.data.files);

            setWorkspace(prev => ({
                ...prev,
                files: result.data.files,
                savedFiles: { ...result.data.files },
                fileTree,
                hasUnsavedChanges: false
            }));

            console.log('✅ Workspace reverted from filesystem successfully:', result.data.count, 'files');
        } catch (error) {
            console.error('❌ Error reverting workspace:', error);
            setError('Failed to revert workspace from filesystem');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefresh = async () => {
        setIsInitialized(false);
        await initializeWorkspace();
    };

    const saveFilesToFilesystem = async (files: { [path: string]: string }) => {
        try {
            const token = localStorage.getItem('token');

            // Generate file changes using diff algorithm for auto-save
            const changes = generateFileChanges(workspace.savedFiles, files);

            // Use optimized endpoint if beneficial, otherwise use full save
            let response;
            if (changes.length > 0) {
                const payloadStats = calculatePayloadReduction(files, changes);

                if (payloadStats.reduction > 10) {
                    // Use optimized diff-based endpoint for auto-save
                    response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/apply-changes`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({ changes })
                    });
                } else {
                    // Use full save when diff isn't beneficial
                    response = await fetch(`http://localhost:3001/api/workspace/${workspaceNameRef.current}/save`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({ files })
                    });
                }
            } else {
                // No changes detected, skip save
                console.log('📝 No changes detected, skipping auto-save');
                return;
            }

            if (!response.ok) {
                throw new Error('Failed to save files to filesystem');
            }

            console.log('✅ Files auto-saved to filesystem successfully');
        } catch (error) {
            console.error('❌ Error auto-saving files:', error);
        }
    };

    const updateFilesFromChat = (newFiles: { [path: string]: string }) => {
        console.log('📝 Updating files from chat response:', Object.keys(newFiles));

        setWorkspace(prev => {
            const updatedFiles = { ...prev.files, ...newFiles };
            const updatedFileTree = createFileTreeFromFiles(updatedFiles);
            const hasChanges = Object.keys(updatedFiles).some(
                path => updatedFiles[path] !== prev.savedFiles[path]
            );

            return {
                ...prev,
                files: updatedFiles,
                fileTree: updatedFileTree,
                hasUnsavedChanges: hasChanges
            };
        });
    };

    // Expose update function globally for chat integration
    useEffect(() => {
        if (interviewUuid) {
            (window as any)[`monacoWorkspace_${interviewUuid}`] = {
                updateFiles: updateFilesFromChat
            };
        }

        return () => {
            if (interviewUuid) {
                delete (window as any)[`monacoWorkspace_${interviewUuid}`];
            }
        };
    }, [interviewUuid]);

    const handleCreateFile = () => {
        if (!newFileName.trim()) return;

        const filePath = newFileName.trim();
        const content = newFileType === 'file' ? '' : undefined;

        if (newFileType === 'file') {
            // Create new file
            setWorkspace(prev => {
                const newFiles = { ...prev.files, [filePath]: content || '' };
                const newFileTree = createFileTreeFromFiles(newFiles);

                return {
                    ...prev,
                    files: newFiles,
                    fileTree: newFileTree,
                    selectedFile: filePath,
                    hasUnsavedChanges: true
                };
            });
        }

        setNewFileDialog(false);
        setNewFileName('');
    };

    const toggleFolder = (folderPath: string) => {
        const updateTree = (nodes: FileNode[]): FileNode[] => {
            return nodes.map(node => {
                if (node.path === folderPath && node.type === 'folder') {
                    return { ...node, isExpanded: !node.isExpanded };
                }
                if (node.children) {
                    return { ...node, children: updateTree(node.children) };
                }
                return node;
            });
        };

        setWorkspace(prev => ({
            ...prev,
            fileTree: updateTree(prev.fileTree)
        }));
    };

    const renderFileTree = (nodes: FileNode[], depth: number = 0): React.ReactNode => {
        return nodes.map(node => (
            <React.Fragment key={node.path}>
                <ListItem
                    button
                    onClick={() => {
                        if (node.type === 'folder') {
                            toggleFolder(node.path);
                        } else {
                            handleFileSelect(node.path);
                        }
                    }}
                    selected={workspace.selectedFile === node.path}
                    sx={{
                        pl: 2 + depth * 2,
                        py: 0.5,
                        minHeight: 32,
                        cursor: 'pointer',
                        '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.1)'
                        },
                        '&.Mui-selected': {
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            '&:hover': {
                                backgroundColor: 'rgba(102, 126, 234, 0.2)'
                            }
                        }
                    }}
                >
                    <ListItemIcon sx={{ minWidth: 24, color: '#ffffff' }}>
                        {node.type === 'folder' ? (
                            node.isExpanded ? <FolderOpenIcon fontSize="small" sx={{ color: '#ffd700' }} /> : <FolderIcon fontSize="small" sx={{ color: '#ffd700' }} />
                        ) : (
                            <FileIcon fontSize="small" sx={{ color: '#87ceeb' }} />
                        )}
                    </ListItemIcon>
                    <ListItemText
                        primary={node.name}
                        primaryTypographyProps={{
                            fontSize: '0.875rem',
                            fontWeight: workspace.selectedFile === node.path ? 'bold' : 'normal',
                            color: '#ffffff' // White text for better visibility
                        }}
                    />
                    {node.type === 'folder' && (
                        <IconButton size="small" onClick={(e) => {
                            e.stopPropagation();
                            toggleFolder(node.path);
                        }} sx={{ color: '#ffffff' }}>
                            {node.isExpanded ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                        </IconButton>
                    )}
                </ListItem>
                {node.type === 'folder' && node.isExpanded && node.children && (
                    <Collapse in={node.isExpanded}>
                        {renderFileTree(node.children, depth + 1)}
                    </Collapse>
                )}
            </React.Fragment>
        ));
    };

    const selectedFileContent = workspace.selectedFile ? workspace.files[workspace.selectedFile] || '' : '';
    const selectedFileLanguage = workspace.selectedFile ? getLanguageFromFilename(workspace.selectedFile) : 'text';

    return (
        <Box sx={{
            height: height === '100%' ? '100%' : height || '700px', // Use dynamic height when 100%
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            flex: height === '100%' ? 1 : 'none', // Allow growing when height is 100%
            minHeight: minHeight || '500px' // Ensure minimum usable height
        }}>
            {/* Workspace Header */}
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 1,
                    backgroundColor: '#2d2d2d',
                    borderBottom: '1px solid #444',
                    flexShrink: 0
                }}
            >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                        Monaco Workspace
                    </Typography>
                    {isLoading && <CircularProgress size={16} sx={{ color: 'orange' }} />}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                        size="small"
                        onClick={handleSave}
                        disabled={!workspace.hasUnsavedChanges || isLoading}
                        startIcon={<SaveIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: workspace.hasUnsavedChanges ? '#4caf50' : '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        Save
                    </Button>

                    <Button
                        size="small"
                        onClick={handleRevert}
                        disabled={!workspace.hasUnsavedChanges || isLoading}
                        startIcon={<RevertIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: workspace.hasUnsavedChanges ? '#ff9800' : '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        Revert
                    </Button>

                    <Button
                        size="small"
                        onClick={handleRefresh}
                        startIcon={<RefreshIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        Refresh
                    </Button>
                </Box>
            </Box>

            {/* Error Display */}
            {error && (
                <Alert severity="error" sx={{ m: 1, flexShrink: 0 }}>
                    {error}
                </Alert>
            )}

            {/* Loading State */}
            {isLoading && !isInitialized && (
                <Box
                    sx={{
                        flex: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#1e1e1e',
                        height: '100%'
                    }}
                >
                    <Box sx={{ textAlign: 'center', color: '#cccccc' }}>
                        <CircularProgress size={40} sx={{ mb: 2, color: '#667eea' }} />
                        <Typography variant="body1">
                            Initializing Monaco workspace...
                        </Typography>
                    </Box>
                </Box>
            )}

            {/* Main Workspace Content */}
            {isInitialized && (
                <Box sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: { xs: 'column', lg: 'row' }, // Stack vertically on mobile/tablet
                    gap: 1, // Add gap between sections
                    p: 1, // Add padding around the workspace
                    backgroundColor: '#f5f5f5',
                    overflow: 'hidden',
                    minHeight: 0 // Important for flex children to shrink
                }}>
                    {/* File Explorer */}
                    <Paper
                        elevation={2}
                        sx={{
                            width: { xs: '100%', lg: 320 }, // Responsive width
                            minWidth: { lg: 280 }, // Minimum width on larger screens
                            backgroundColor: '#252526',
                            display: 'flex',
                            flexDirection: 'column',
                            height: { xs: '250px', lg: '100%' }, // Fixed height on mobile, full on desktop
                            overflow: 'hidden',
                            flexShrink: 0, // Prevent shrinking
                            borderRadius: 2
                        }}
                    >
                        <Box
                            sx={{
                                p: 1,
                                borderBottom: '1px solid #444',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between'
                            }}
                        >
                            <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                                Explorer
                            </Typography>
                            <IconButton
                                size="small"
                                onClick={() => setNewFileDialog(true)}
                                sx={{ color: '#ffffff' }}
                            >
                                <AddIcon fontSize="small" />
                            </IconButton>
                        </Box>

                        <List sx={{
                            flex: 1,
                            py: 0,
                            overflow: 'auto',
                            minHeight: 0, // Important for flex shrinking
                            height: '100%',
                            '&::-webkit-scrollbar': {
                                width: '8px',
                            },
                            '&::-webkit-scrollbar-track': {
                                background: '#2d2d2d',
                            },
                            '&::-webkit-scrollbar-thumb': {
                                background: '#555',
                                borderRadius: '4px',
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                                background: '#777',
                            },
                        }}>
                            {renderFileTree(workspace.fileTree)}
                        </List>
                    </Paper>

                    {/* Editor and Preview */}
                    <Box sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: { xs: 'column', lg: 'row' },
                        gap: 1,
                        height: { xs: 'calc(100% - 250px)', lg: '100%' }, // Account for file explorer on mobile
                        overflow: 'hidden',
                        minHeight: { xs: '400px', lg: 0 }, // Minimum height on mobile
                        minWidth: 0 // Important for flex shrinking
                    }}>
                        {/* Code Editor */}
                        <Paper
                            elevation={2}
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                                borderRadius: 2,
                                backgroundColor: '#1e1e1e'
                            }}
                        >
                            {workspace.selectedFile ? (
                                <>
                                    <Box
                                        sx={{
                                            p: 1.5,
                                            borderBottom: '1px solid #444',
                                            backgroundColor: '#2d2d2d',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between'
                                        }}
                                    >
                                        <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                                            {workspace.selectedFile}
                                        </Typography>
                                        {workspace.hasUnsavedChanges && (
                                            <Typography variant="caption" sx={{ color: '#ff9800', fontWeight: 'bold' }}>
                                                ● Unsaved changes
                                            </Typography>
                                        )}
                                    </Box>

                                    <Box sx={{
                                        flex: 1,
                                        overflow: 'hidden',
                                        minHeight: 0, // Important for flex shrinking
                                        height: '100%'
                                    }}>
                                        <Editor
                                            height="100%"
                                            language={selectedFileLanguage}
                                            value={selectedFileContent}
                                            onChange={handleEditorChange}
                                            onMount={(editor) => {
                                                editorRef.current = editor;
                                            }}
                                            theme="vs-dark"
                                            options={{
                                                minimap: { enabled: window.innerWidth > 1024 }, // Enable minimap only on larger screens
                                                fontSize: window.innerWidth < 768 ? 12 : 14, // Smaller font on mobile
                                                lineNumbers: 'on',
                                                roundedSelection: false,
                                                scrollBeyondLastLine: false,
                                                automaticLayout: true,
                                                wordWrap: 'on',
                                                scrollbar: {
                                                    vertical: 'visible',
                                                    horizontal: 'visible',
                                                    verticalScrollbarSize: window.innerWidth < 768 ? 8 : 12,
                                                    horizontalScrollbarSize: window.innerWidth < 768 ? 8 : 12
                                                },
                                                // Better mobile experience
                                                glyphMargin: window.innerWidth > 768,
                                                folding: window.innerWidth > 768,
                                                lineDecorationsWidth: window.innerWidth < 768 ? 10 : 20,
                                                lineNumbersMinChars: window.innerWidth < 768 ? 3 : 5
                                            }}
                                        />
                                    </Box>
                                </>
                            ) : (
                                <Box
                                    sx={{
                                        flex: 1,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#cccccc',
                                        height: '100%'
                                    }}
                                >
                                    <Typography variant="h6" sx={{ opacity: 0.7 }}>
                                        Select a file to start editing
                                    </Typography>
                                </Box>
                            )}
                        </Paper>

                        {/* Live Preview */}
                        <Paper
                            elevation={2}
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                                borderRadius: 2,
                                backgroundColor: '#ffffff',
                                minHeight: { xs: '300px', lg: 0 }
                            }}
                        >
                            <Box
                                sx={{
                                    p: 1.5,
                                    borderBottom: '1px solid #e0e0e0',
                                    backgroundColor: '#f5f5f5',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between'
                                }}
                            >
                                <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#333' }}>
                                    Live Preview
                                </Typography>
                                <IconButton
                                    size="small"
                                    onClick={() => {
                                        const iframe = document.getElementById('live-preview-iframe') as HTMLIFrameElement;
                                        if (iframe) {
                                            iframe.src = iframe.src; // Refresh iframe
                                        }
                                    }}
                                    sx={{ color: '#666' }}
                                >
                                    <RefreshIcon fontSize="small" />
                                </IconButton>
                            </Box>

                            <Box sx={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
                                <iframe
                                    id="live-preview-iframe"
                                    src="http://localhost:8080"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        border: 'none',
                                        backgroundColor: 'white'
                                    }}
                                    title="Live Preview"
                                    onError={(e) => {
                                        console.error('Preview iframe error:', e);
                                    }}
                                />
                            </Box>
                        </Paper>
                    </Box>
                </Box>
            )}

            {/* New File Dialog */}
            <Dialog open={newFileDialog} onClose={() => setNewFileDialog(false)}>
                <DialogTitle>Create New {newFileType === 'file' ? 'File' : 'Folder'}</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label={`${newFileType === 'file' ? 'File' : 'Folder'} Name`}
                        fullWidth
                        variant="outlined"
                        value={newFileName}
                        onChange={(e) => setNewFileName(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setNewFileDialog(false)}>Cancel</Button>
                    <Button
                        onClick={() => {
                            handleCreateFile();
                        }}
                        disabled={!newFileName.trim()}
                    >
                        Create
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default MonacoWorkspace;